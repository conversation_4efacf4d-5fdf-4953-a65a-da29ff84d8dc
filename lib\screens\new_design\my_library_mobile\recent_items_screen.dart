import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

class RecentItemsScreen extends StatefulWidget {
  final List<Map<String, dynamic>> allData;

  const RecentItemsScreen({Key? key, required this.allData}) : super(key: key);

  @override
  State<RecentItemsScreen> createState() => _RecentItemsScreenState();
}

class _RecentItemsScreenState extends State<RecentItemsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showSearchBar = false;
bool _hasShownSearchBarOnce = false;


  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  int _currentPage = 0;
  final int _itemsPerPage = 10;
  String? _selectedCategoryFilter;
  int? _hoveredRowIndex;
  bool _isSearchActive = false;

  List<Map<String, dynamic>> get _filteredData {
    List<Map<String, dynamic>> filtered = List.from(widget.allData);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) {
        final fileName = item['fileName'].toString().toLowerCase();
        final type = item['type'].toString().toLowerCase();
        final query = _searchQuery.toLowerCase();
        return fileName.contains(query) || type.contains(query);
      }).toList();
    }

    // Apply category filter
    if (_selectedCategoryFilter != null) {
      filtered = filtered
          .where((item) => item['type'] == _selectedCategoryFilter)
          .toList();
    }

    // Sort the data by last opened (most recent first)
    filtered.sort((a, b) {
      DateTime aDate = a['lastOpened'];
      DateTime bDate = b['lastOpened'];
      return bDate.compareTo(aDate);
    });

    return filtered;
  }

  List<Map<String, dynamic>> get _paginatedData {
    final startIndex = _currentPage * _itemsPerPage;
    final endIndex =
        (startIndex + _itemsPerPage).clamp(0, _filteredData.length);
    return _filteredData.sublist(startIndex, endIndex);
  }

  int get _totalPages => (_filteredData.length / _itemsPerPage).ceil();

  @override
  void initState() {
    super.initState();

   _scrollController.addListener(() {
  if (_scrollController.position.userScrollDirection == ScrollDirection.forward) {
    // Show search bar ONCE
    if (!_hasShownSearchBarOnce) {
      setState(() {
        _showSearchBar = true;
        _hasShownSearchBarOnce = true;
      });
    }
  }
});

  }

 @override
void dispose() {
  _scrollController.dispose();
  _searchController.dispose();
  super.dispose();
}


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: SvgPicture.asset(
            'assets/images/my_library/create_back.svg', // replace with your actual SVG path
            width: 24,
            height: 24,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        backgroundColor: Color(0xffF7F9FB),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
        actions: [],
      ),
      body: Column(
        children: [
          // Search bar (appears when search is active)
          AnimatedSwitcher(
            duration: Duration(milliseconds: 300),
            child: _showSearchBar
                ? Padding(
                    key: ValueKey(true),
                    padding: const EdgeInsets.all(16.0),
                    child: _buildSearchBar(),
                  )
                : SizedBox(key: ValueKey(false), height: 0),
          ),

          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      controller: _scrollController, // Add this
                      child: Column(
                        children: [
                          _buildTable(),
                          const SizedBox(height: 16),
                          _buildPagination(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        border: Border.all(color: Colors.grey.shade300, width: 0.5),
      ),
      child: Row(
        children: [
          // Search input field
          Expanded(
            child: TextField(
              controller: _searchController,
              autofocus: false,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _currentPage = 0;
                });
              },
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0),
                isDense: true,
                hintText: 'Search',
                hintStyle: TextStyle(
                  color: Color(0xffD0D0D0),
                  fontSize: 14,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              style: TextStyle(
                fontSize: 14,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              onSubmitted: (value) {
                FocusScope.of(context).unfocus();
              },
            ),
          ),

          // Close icon
          GestureDetector(
            onTap: () {
              setState(() {
                _isSearchActive = false;
                _searchController.clear();
                _searchQuery = '';
                _currentPage = 0;
              });
              FocusScope.of(context).unfocus();
            },
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: ConstrainedBox(
                constraints: BoxConstraints.tightFor(width: 40, height: 40),
                child: Align(
                  alignment: Alignment.center,
                  child: Icon(
                    Icons.close,
                    size: 20,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // _buildTableHeader(),
          ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: _paginatedData.length,
            itemBuilder: (context, index) {
              return _buildTableRow(_paginatedData[index], index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
          top: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // File Name - Left aligned
          Expanded(
            flex: 4,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'File Name',
                style: TextStyle(
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.black),
              ),
            ),
          ),
          // Last Opened
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Last Opened',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          // Favorites column - empty header for star icon space
          Expanded(
            flex: 1,
            child: Container(),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(Map<String, dynamic> item, int index) {
    final isEvenRow = index % 2 == 0;
    final isHovered = _hoveredRowIndex == index;
    final isFavorite = item['isFavorite'] ?? false;

    return MouseRegion(
      onEnter: (_) => setState(() => _hoveredRowIndex = index),
      onExit: (_) => setState(() => _hoveredRowIndex = null),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isEvenRow ? Colors.grey.shade50 : Colors.white,
          border: Border(
            bottom: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
          ),
        ),
        child: Row(
          children: [
            // File Name - Left aligned
            Expanded(
              flex: 4,
              child: Row(
                children: [
                  _buildTypeIcon(item['type']),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['fileName'],
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimaryLight,
                          ),
                        ),
                        if (item['type'].isNotEmpty)
                          Text(
                            item['type'],
                            style: TextStyle(
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontSize: 10,
                              fontWeight: FontWeight.w400,
                              color: AppColors.textGreyColor,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Last Opened
            Expanded(
              flex: 3,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  _formatDate(item['lastOpened']),
                  style: TextStyle(
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: AppColors.textGreyColor,
                  ),
                ),
              ),
            ),
            // Favorites column - hover-based star visibility
            Expanded(
              flex: 1,
              child: Align(
                alignment: Alignment.centerRight,
                child: Visibility(
                  visible: isHovered || isFavorite,
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          item['isFavorite'] = !item['isFavorite'];
                        });
                      },
                      child: Icon(
                        item['isFavorite'] ? Icons.star : Icons.star_border,
                        size: 20,
                        color: item['isFavorite']
                            ? Colors.amber
                            : AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  Widget _buildTypeIcon(String type) {
    String svgAsset;

    switch (type) {
      case 'Role':
        svgAsset = 'assets/images/my_library/create_role.svg';
        break;
      case 'Object':
        svgAsset = 'assets/images/my_library/create_object.svg';
        break;
      case 'Solution':
        svgAsset = 'assets/images/my_library/create_solution.svg';
        break;
      case 'Project':
        svgAsset = 'assets/images/my_library/create_project.svg';
        break;
      default:
        svgAsset = 'assets/icons/default.svg';
    }

    return Center(
      child: CustomImage.asset(
        svgAsset,
        width: 32,
        height: 32,
        fit: BoxFit.contain,
      ).toWidget(),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday, ${date.day}/${date.month}/${date.year}';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildPagination() {
    if (_totalPages <= 1) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppSpacing.md),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Navigation buttons
          Row(
            children: [
              // Previous button
              _HoverPaginationButton(
                icon: const Icon(Icons.chevron_left, size: 20),
                onPressed: _currentPage > 0
                    ? () {
                        setState(() {
                          _currentPage--;
                        });
                      }
                    : null,
              ),
              const SizedBox(width: 8),
              // Next button
              _HoverPaginationButton(
                icon: const Icon(Icons.chevron_right, size: 20),
                onPressed: _currentPage < _totalPages - 1
                    ? () {
                        setState(() {
                          _currentPage++;
                        });
                      }
                    : null,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.onPressed == null
                ? Colors.grey.shade200
                : (isHovered ? Color(0xff0058FF) : Colors.grey.shade300),
            width: 1.0,
          ),
          borderRadius: isHovered ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: widget.onPressed == null
              ? Colors.grey.shade400
              : (isHovered ? Color(0xff0058FF) : Colors.black),
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}
