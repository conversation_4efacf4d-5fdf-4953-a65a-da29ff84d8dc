import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppTheme {
  // Text Styles
  static TextTheme _buildTextTheme(TextTheme base, bool isDark) {
    Color primaryTextColor =
        isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight;
    Color secondaryTextColor =
        isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight;

    return base.copyWith(
      // SF Pro Text for headlines and UI elements
      displayLarge: base.displayLarge!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: primaryTextColor,
      ),
      displayMedium: base.displayMedium!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: primaryTextColor,
      ),
      displaySmall: base.displaySmall!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: primaryTextColor,
      ),
      headlineMedium: base.headlineMedium!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: primaryTextColor,
      ),
      titleLarge: base.titleLarge!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: primaryTextColor,
      ),
      titleMedium: base.titleMedium!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: primaryTextColor,
      ),
      titleSmall: base.titleSmall!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: secondaryTextColor,
      ),

      // Tiempos Text for body content
      bodyLarge: base.bodyLarge!.copyWith(
        fontFamily: 'TiemposText',
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: primaryTextColor,
        height: 1.5,
      ),
      bodyMedium: base.bodyMedium!.copyWith(
        fontFamily: 'TiemposText',
        fontSize: 15,
        fontWeight: FontWeight.w400,
        color: primaryTextColor,
        height: 1.5,
      ),
      bodySmall: base.bodySmall!.copyWith(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: secondaryTextColor,
        height: 1.5,
      ),

      // SF Pro Text for buttons and labels
      labelLarge: base.labelLarge!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: primaryTextColor,
      ),
      labelMedium: base.labelMedium!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: primaryTextColor,
      ),
      labelSmall: base.labelSmall!.copyWith(
        fontFamily: 'SFProText',
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: secondaryTextColor,
      ),
    );
  }

  // Light Theme
  static ThemeData lightTheme() {
    final ThemeData base = ThemeData.light();
    return base.copyWith(
      colorScheme: ColorScheme.light(
        primary: AppColors.primaryIndigo,
        onPrimary: Colors.white,
        secondary: AppColors.primaryIndigoLight,
        onSecondary: Colors.white,
        surface: AppColors.surfaceLight,
        onSurface: AppColors.textPrimaryLight,
        error: AppColors.error,
        onError: Colors.white,
      ),
      textTheme: _buildTextTheme(base.textTheme, false),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryIndigo,
        foregroundColor: Colors.white,
        elevation: 0,
        titleTextStyle: TextStyle(
          fontFamily: 'SFProText',
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      scaffoldBackgroundColor: AppColors.backgroundLight,
      cardTheme: CardThemeData(
        color: AppColors.surfaceLight,
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      buttonTheme: ButtonThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        buttonColor: AppColors.primaryIndigo,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryIndigo,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          textStyle: TextStyle(
            fontFamily: 'SFProText',
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  // Dark Theme
  static ThemeData darkTheme() {
    final ThemeData base = ThemeData.dark();
    return base.copyWith(
      colorScheme: ColorScheme.dark(
        primary: AppColors.subtleIndigoDark,
        onPrimary: Colors.white,
        secondary: AppColors.subtleIndigoSurface,
        onSecondary: Colors.white,
        surface: AppColors.surfaceDark,
        onSurface: AppColors.textPrimaryDark,
        error: AppColors.error,
        onError: Colors.white,
      ),
      textTheme: _buildTextTheme(base.textTheme, true),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.subtleIndigoSurface,
        foregroundColor: Colors.white,
        elevation: 0,
        titleTextStyle: TextStyle(
          fontFamily: 'SFProText',
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      scaffoldBackgroundColor: AppColors.backgroundDark,
      cardTheme: CardThemeData(
        color: AppColors.surfaceDark,
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      buttonTheme: ButtonThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        buttonColor: AppColors.subtleIndigoDark,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.subtleIndigoDark,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          textStyle: TextStyle(
            fontFamily: 'SFProText',
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
