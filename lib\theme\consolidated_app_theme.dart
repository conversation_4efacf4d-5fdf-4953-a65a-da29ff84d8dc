import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_theme_constants.dart';

/// Consolidated AppTheme class that provides consistent theming for the entire application
class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();
  
  // Text Styles
  static TextTheme _buildTextTheme(TextTheme base, bool isDark) {
    Color primaryTextColor = isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight;
    Color secondaryTextColor = isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight;

    return base.copyWith(
      // SF Pro Text for headlines and UI elements
      displayLarge: base.displayLarge!.copyWith(
        fontFamily: AppThemeConstants.sfProTextFontFamily,
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: primaryTextColor,
      ),
      displayMedium: base.displayMedium!.copyWith(
        fontFamily: AppThemeConstants.sfProTextFontFamily,
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: primaryTextColor,
      ),
      displaySmall: base.displaySmall!.copyWith(
        fontFamily: AppThemeConstants.sfProTextFontFamily,
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: primaryTextColor,
      ),
      headlineMedium: base.headlineMedium!.copyWith(
        fontFamily: AppThemeConstants.sfProTextFontFamily,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: primaryTextColor,
      ),
      titleLarge: base.titleLarge!.copyWith(
        fontFamily: AppThemeConstants.sfProTextFontFamily,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: primaryTextColor,
      ),
      titleMedium: base.titleMedium!.copyWith(
        fontFamily: AppThemeConstants.sfProTextFontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: primaryTextColor,
      ),
      titleSmall: base.titleSmall!.copyWith(
        fontFamily: AppThemeConstants.sfProTextFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: secondaryTextColor,
      ),

      // Tiempos Text for body content
      bodyLarge: base.bodyLarge!.copyWith(
        fontFamily: AppThemeConstants.tiemposTextFontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: primaryTextColor,
        height: 1.5,
      ),
      bodyMedium: base.bodyMedium!.copyWith(
        fontFamily: AppThemeConstants.tiemposTextFontFamily,
        fontSize: 15,
        fontWeight: FontWeight.w400,
        color: primaryTextColor,
        height: 1.5,
      ),
      bodySmall: base.bodySmall!.copyWith(
        fontFamily: AppThemeConstants.tiemposTextFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: secondaryTextColor,
        height: 1.5,
      ),

      // SF Pro Text for buttons and labels
      labelLarge: base.labelLarge!.copyWith(
        fontFamily: AppThemeConstants.sfProTextFontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: primaryTextColor,
      ),
      labelMedium: base.labelMedium!.copyWith(
        fontFamily: AppThemeConstants.sfProTextFontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: primaryTextColor,
      ),
      labelSmall: base.labelSmall!.copyWith(
        fontFamily: AppThemeConstants.sfProTextFontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: secondaryTextColor,
      ),
    );
  }

  // Light Theme
  static ThemeData lightTheme() {
    final ThemeData base = ThemeData.light();
    return base.copyWith(
      colorScheme: ColorScheme.light(
        //primary: AppColors.primaryIndigo,
        primary: const Color(0xFF0058FF),
        onPrimary: Colors.white,
        secondary: AppColors.primaryIndigoLight,
        onSecondary: Colors.white,
        surface: AppColors.surfaceLight,
        onSurface: AppColors.textPrimaryLight,
        surfaceContainer: AppColors.backgroundLight,
        error: AppColors.error,
        onError: Colors.white,
      ),
      textTheme: _buildTextTheme(base.textTheme, false),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryIndigo,
        foregroundColor: Colors.white,
        elevation: 0,
        titleTextStyle: TextStyle(
          fontFamily: AppThemeConstants.sfProTextFontFamily,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      scaffoldBackgroundColor: AppColors.backgroundLight,
      cardTheme: CardThemeData(
        color: AppColors.surfaceLight,
        elevation: AppThemeConstants.elevationM,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
        ),
      ),
      buttonTheme: ButtonThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
        ),
        buttonColor: AppColors.primaryIndigo,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryIndigo,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          ),
          padding: AppThemeConstants.buttonPaddingM,
          textStyle: TextStyle(
            fontFamily: AppThemeConstants.sfProTextFontFamily,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primaryIndigo,
          side: BorderSide(color: AppColors.primaryIndigo),
          padding: AppThemeConstants.buttonPaddingM,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primaryIndigo,
          padding: EdgeInsets.symmetric(
            horizontal: AppThemeConstants.spacingM, 
            vertical: AppThemeConstants.spacingS
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.surfaceLight,
        contentPadding: AppThemeConstants.inputPaddingM,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          borderSide: BorderSide(color: AppColors.primaryIndigo),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          borderSide: BorderSide(color: AppColors.primaryIndigo),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          borderSide: BorderSide(color: AppColors.primaryIndigo),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          borderSide: BorderSide(color: AppColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          borderSide: BorderSide(color: AppColors.error),
        ),
      ),
      dividerTheme: DividerThemeData(
        color: Color(0xFFE5E5E5),
        thickness: 1,
        space: AppThemeConstants.spacingM,
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.primaryIndigoLight,
        contentTextStyle: AppThemeConstants.getBodyMedium(false).copyWith(color: Colors.white),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
        ),
      ),
    );
  }

  // Dark Theme
  static ThemeData darkTheme() {
    final ThemeData base = ThemeData.dark();
    return base.copyWith(
      colorScheme: ColorScheme.dark(
        primary: AppColors.subtleIndigoDark,
        onPrimary: Colors.white,
        secondary: AppColors.subtleIndigoSurface,
        onSecondary: Colors.white,
        surface: AppColors.surfaceDark,
        onSurface: AppColors.textPrimaryDark,
        surfaceContainer: AppColors.backgroundDark,
        error: AppColors.error,
        onError: Colors.white,
      ),
      textTheme: _buildTextTheme(base.textTheme, true),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.subtleIndigoSurface,
        foregroundColor: Colors.white,
        elevation: 0,
        titleTextStyle: TextStyle(
          fontFamily: AppThemeConstants.sfProTextFontFamily,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      scaffoldBackgroundColor: AppColors.backgroundDark,
      cardTheme: CardThemeData(
        color: AppColors.surfaceDark,
        elevation: AppThemeConstants.elevationM,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
        ),
      ),
      buttonTheme: ButtonThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
        ),
        buttonColor: AppColors.subtleIndigoDark,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.subtleIndigoDark,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          ),
          padding: AppThemeConstants.buttonPaddingM,
          textStyle: TextStyle(
            fontFamily: AppThemeConstants.sfProTextFontFamily,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.subtleIndigoDark,
          side: BorderSide(color: AppColors.subtleIndigoDark),
          padding: AppThemeConstants.buttonPaddingM,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.subtleIndigoDark,
          padding: EdgeInsets.symmetric(
            horizontal: AppThemeConstants.spacingM, 
            vertical: AppThemeConstants.spacingS
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.surfaceDark,
        contentPadding: AppThemeConstants.inputPaddingM,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          borderSide: BorderSide(color: AppColors.subtleIndigoDark),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          borderSide: BorderSide(color: AppColors.subtleIndigoDark),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          borderSide: BorderSide(color: AppColors.subtleIndigoDark),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          borderSide: BorderSide(color: AppColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
          borderSide: BorderSide(color: AppColors.error),
        ),
      ),
      dividerTheme: DividerThemeData(
        color: Color(0xFF374151), // Darker divider for dark theme
        thickness: 1,
        space: AppThemeConstants.spacingM,
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.subtleIndigoSurface,
        contentTextStyle: AppThemeConstants.getBodyMedium(true).copyWith(color: Colors.white),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppThemeConstants.borderRadiusM),
        ),
      ),
    );
  }
}
