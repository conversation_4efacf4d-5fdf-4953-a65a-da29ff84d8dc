import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

// Import your app colors to use the primary color for text selection
// Assuming the app_colors.dart is in the main app, we'll define the primary color here
// You may need to adjust this import path based on your project structure
const Color _primaryBlue = Color(0xff0058FF);

/// A rich text widget using flutter_quill for proper text selection and formatting
class QuillRichTextWidget extends StatefulWidget {
  // Content properties
  final String initialText;
  final Map<String, dynamic>? jsonData;
  final String displayKey;
  final bool isEditable;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color hoverColor;
  final double fontSize;
  final FontWeight fontWeight;
  final String? fontFamily;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Layout properties
  final double? width;
  final double compactHeight;
  final double expandedHeight;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final bool hasBorder;
  final Color borderColor;
  final Color hoverBorderColor;
  final double borderWidth;
  final bool hasShadow;
  final double elevation;

  // Toolbar properties
  final bool showToolbar;
  final bool useCompactToolbar;
  final bool showBoldButton;
  final bool showItalicButton;
  final bool showUnderlineButton;
  final bool showStrikethroughButton;
  final bool showColorPicker;
  final bool showFontSizePicker;
  final bool showAlignmentButtons;
  final bool showBulletList;
  final bool showNumberedList;
  final bool showLinkButton;
  final bool showClearFormattingButton;

  // Active button styling
  final Color activeButtonBackgroundColor;
  final Color activeButtonIconColor;
  final double activeButtonBorderRadius;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool autofocus;
  final Duration animationDuration;

  // Callbacks
  final Function(String)? onTextChanged;
  final Function()? onTap;
  final Function()? onExpand;
  final Function(bool)? onHover;
  final Function(bool)? onFocus;

  const QuillRichTextWidget({
    super.key,
    this.initialText = 'Type Here',
    this.jsonData,
    this.displayKey = 'text',
    this.isEditable = true,
    this.textColor = const Color(0xFF333333),
    this.backgroundColor = Colors.white,
    this.hoverColor = const Color(0xFFF5F5F5),
    this.fontSize = 14.0,
    this.fontWeight = FontManager.medium,
    this.fontFamily = FontManager.fontFamilyInter,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.width,
    this.compactHeight = 40.0,
    this.expandedHeight = 120.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
    this.margin = EdgeInsets.zero,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.borderColor = const Color(0xFFCCCCCC),
    this.hoverBorderColor = const Color(0xFF0058FF),
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.showToolbar = true,
    this.useCompactToolbar = true,
    this.showBoldButton = true,
    this.showItalicButton = true,
    this.showUnderlineButton = true,
    this.showStrikethroughButton = true,
    this.showColorPicker = true,
    this.showFontSizePicker = true,
    this.showAlignmentButtons = true,
    this.showBulletList = true,
    this.showNumberedList = true,
    this.showLinkButton = true,
    this.showClearFormattingButton = true,
    this.activeButtonBackgroundColor = const Color(0xFFE3F2FD),
    this.activeButtonIconColor = const Color(0xFF0058FF),
    this.activeButtonBorderRadius = 4.0,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.autofocus = false,
    this.animationDuration = const Duration(milliseconds: 200),
    this.onTextChanged,
    this.onTap,
    this.onExpand,
    this.onHover,
    this.onFocus,
  });

  /// Creates a QuillRichTextWidget from JSON data
  factory QuillRichTextWidget.fromJson(Map<String, dynamic> json) {
    return QuillRichTextWidget(
      // Content properties
      initialText: json['initialText'] as String? ?? 'Type Here',
      jsonData: json['jsonData'] as Map<String, dynamic>?,
      displayKey: json['displayKey'] as String? ?? 'text',
      isEditable: json['isEditable'] as bool? ?? true,

      // Appearance properties
      textColor: _parseColor(json['textColor']) ?? Color(0xFF333333),
      backgroundColor: _parseColor(json['backgroundColor']) ?? Colors.white,
      hoverColor: _parseColor(json['hoverColor']) ?? const Color(0xFFF5F5F5),
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 14.0,
      fontWeight: _parseFontWeight(json['fontWeight']) ?? FontWeight.normal,
      fontFamily: json['fontFamily'] as String?,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      textAlign: _parseTextAlign(json['textAlign']) ?? TextAlign.start,

      // Layout properties
      width: (json['width'] as num?)?.toDouble(),
      compactHeight: (json['compactHeight'] as num?)?.toDouble() ?? 40.0,
      expandedHeight: (json['expandedHeight'] as num?)?.toDouble() ?? 120.0,
      padding:
          _parseEdgeInsets(json['padding']) ??
          const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
      margin: _parseEdgeInsets(json['margin']) ?? EdgeInsets.zero,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      borderColor: _parseColor(json['borderColor']) ?? const Color(0xFFCCCCCC),
      hoverBorderColor:
          _parseColor(json['hoverBorderColor']) ?? const Color(0xFF0058FF),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,

      // Toolbar properties
      showToolbar: json['showToolbar'] as bool? ?? true,
      useCompactToolbar: json['useCompactToolbar'] as bool? ?? true,
      showBoldButton: json['showBoldButton'] as bool? ?? true,
      showItalicButton: json['showItalicButton'] as bool? ?? true,
      showUnderlineButton: json['showUnderlineButton'] as bool? ?? true,
      showStrikethroughButton: json['showStrikethroughButton'] as bool? ?? true,
      showColorPicker: json['showColorPicker'] as bool? ?? true,
      showFontSizePicker: json['showFontSizePicker'] as bool? ?? true,
      showAlignmentButtons: json['showAlignmentButtons'] as bool? ?? true,
      showBulletList: json['showBulletList'] as bool? ?? true,
      showNumberedList: json['showNumberedList'] as bool? ?? true,
      showLinkButton: json['showLinkButton'] as bool? ?? true,
      showClearFormattingButton:
          json['showClearFormattingButton'] as bool? ?? true,

      // Behavior properties
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      autofocus: json['autofocus'] as bool? ?? false,
      animationDuration: Duration(
        milliseconds: json['animationDurationMs'] as int? ?? 200,
      ),
    );
  }

  /// Helper method to parse Color from various formats
  static Color? _parseColor(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is int) {
      return Color(colorValue);
    } else if (colorValue is String) {
      // Handle hex color strings
      if (colorValue.startsWith('#')) {
        final hexString = colorValue.substring(1);
        if (hexString.length == 6) {
          return Color(int.parse('FF$hexString', radix: 16));
        } else if (hexString.length == 8) {
          return Color(int.parse(hexString, radix: 16));
        }
      }
      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        case 'red':
          return Colors.red;
        case 'green':
          return Colors.green;
        case 'blue':
          return Colors.blue;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'grey':
        case 'gray':
          return Colors.grey;
        default:
          return null;
      }
    }
    return null;
  }

  /// Helper method to parse FontWeight
  static FontWeight? _parseFontWeight(dynamic weightValue) {
    if (weightValue == null) return null;

    if (weightValue is String) {
      switch (weightValue.toLowerCase()) {
        case 'thin':
          return FontWeight.w100;
        case 'extralight':
          return FontWeight.w200;
        case 'light':
          return FontWeight.w300;
        case 'normal':
        case 'regular':
          return FontWeight.w400;
        case 'medium':
          return FontWeight.w500;
        case 'semibold':
          return FontWeight.w600;
        case 'bold':
          return FontWeight.w700;
        case 'extrabold':
          return FontWeight.w800;
        case 'black':
          return FontWeight.w900;
        default:
          return null;
      }
    } else if (weightValue is int) {
      switch (weightValue) {
        case 100:
          return FontWeight.w100;
        case 200:
          return FontWeight.w200;
        case 300:
          return FontWeight.w300;
        case 400:
          return FontWeight.w400;
        case 500:
          return FontWeight.w500;
        case 600:
          return FontWeight.w600;
        case 700:
          return FontWeight.w700;
        case 800:
          return FontWeight.w800;
        case 900:
          return FontWeight.w900;
        default:
          return null;
      }
    }
    return null;
  }

  /// Helper method to parse TextAlign
  static TextAlign? _parseTextAlign(dynamic alignValue) {
    if (alignValue == null) return null;

    if (alignValue is String) {
      switch (alignValue.toLowerCase()) {
        case 'left':
        case 'start':
          return TextAlign.start;
        case 'right':
        case 'end':
          return TextAlign.end;
        case 'center':
          return TextAlign.center;
        case 'justify':
          return TextAlign.justify;
        default:
          return null;
      }
    }
    return null;
  }

  /// Helper method to parse EdgeInsets
  static EdgeInsetsGeometry? _parseEdgeInsets(dynamic paddingValue) {
    if (paddingValue == null) return null;

    if (paddingValue is Map<String, dynamic>) {
      final top = (paddingValue['top'] as num?)?.toDouble() ?? 0.0;
      final right = (paddingValue['right'] as num?)?.toDouble() ?? 0.0;
      final bottom = (paddingValue['bottom'] as num?)?.toDouble() ?? 0.0;
      final left = (paddingValue['left'] as num?)?.toDouble() ?? 0.0;

      return EdgeInsets.fromLTRB(left, top, right, bottom);
    } else if (paddingValue is num) {
      return EdgeInsets.all(paddingValue.toDouble());
    }
    return null;
  }

  /// Converts the widget configuration to JSON
  Map<String, dynamic> toJson() {
    return {
      'initialText': initialText,
      'jsonData': jsonData,
      'displayKey': displayKey,
      'isEditable': isEditable,
      'textColor': textColor.value,
      'backgroundColor': backgroundColor.value,
      'hoverColor': hoverColor.value,
      'fontSize': fontSize,
      'fontWeight': _fontWeightToString(fontWeight),
      'fontFamily': fontFamily,
      'isDarkTheme': isDarkTheme,
      'textAlign': _textAlignToString(textAlign),
      'width': width,
      'compactHeight': compactHeight,
      'expandedHeight': expandedHeight,
      'padding': _edgeInsetsToMap(padding),
      'margin': _edgeInsetsToMap(margin),
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'borderColor': borderColor.value,
      'hoverBorderColor': hoverBorderColor.value,
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'showToolbar': showToolbar,
      'useCompactToolbar': useCompactToolbar,
      'showBoldButton': showBoldButton,
      'showItalicButton': showItalicButton,
      'showUnderlineButton': showUnderlineButton,
      'showStrikethroughButton': showStrikethroughButton,
      'showColorPicker': showColorPicker,
      'showFontSizePicker': showFontSizePicker,
      'showAlignmentButtons': showAlignmentButtons,
      'showBulletList': showBulletList,
      'showNumberedList': showNumberedList,
      'showLinkButton': showLinkButton,
      'showClearFormattingButton': showClearFormattingButton,
      'isReadOnly': isReadOnly,
      'isDisabled': isDisabled,
      'autofocus': autofocus,
      'animationDurationMs': animationDuration.inMilliseconds,
    };
  }

  /// Helper method to convert FontWeight to string
  static String _fontWeightToString(FontWeight weight) {
    switch (weight) {
      case FontWeight.w100:
        return 'thin';
      case FontWeight.w200:
        return 'extralight';
      case FontWeight.w300:
        return 'light';
      case FontWeight.w400:
        return 'normal';
      case FontWeight.w500:
        return 'medium';
      case FontWeight.w600:
        return 'semibold';
      case FontWeight.w700:
        return 'bold';
      case FontWeight.w800:
        return 'extrabold';
      case FontWeight.w900:
        return 'black';
      default:
        return 'normal';
    }
  }

  /// Helper method to convert TextAlign to string
  static String _textAlignToString(TextAlign align) {
    switch (align) {
      case TextAlign.start:
        return 'start';
      case TextAlign.end:
        return 'end';
      case TextAlign.center:
        return 'center';
      case TextAlign.justify:
        return 'justify';
      case TextAlign.left:
        return 'left';
      case TextAlign.right:
        return 'right';
      default:
        return 'start';
    }
  }

  /// Helper method to convert EdgeInsets to Map
  static Map<String, double> _edgeInsetsToMap(EdgeInsetsGeometry padding) {
    if (padding is EdgeInsets) {
      return {
        'top': padding.top,
        'right': padding.right,
        'bottom': padding.bottom,
        'left': padding.left,
      };
    }
    return {'top': 0, 'right': 0, 'bottom': 0, 'left': 0};
  }

  @override
  State<QuillRichTextWidget> createState() => _QuillRichTextWidgetState();
}

enum RichTextDisplayMode { compact, hover, editing, expanded }

/// Helper class to define toolbar items with priority and width
class ToolbarItem {
  final Widget widget;
  final double width;
  final int priority;

  const ToolbarItem({
    required this.widget,
    required this.width,
    required this.priority,
  });
}

class _QuillRichTextWidgetState extends State<QuillRichTextWidget>
    with TickerProviderStateMixin {
  late QuillController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _heightAnimation;
  late Animation<Color?> _colorAnimation;
  late Animation<Color?> _borderColorAnimation;

  RichTextDisplayMode _currentMode = RichTextDisplayMode.compact;
  bool _isHovered = false;
  bool _isFocused = false;
  String _currentText = '';

  @override
  void initState() {
    super.initState();

    // Initialize text from JSON data or initial text
    _currentText = _getDisplayText();

    // Initialize Quill controller with initial text
    _controller = QuillController.basic();
    if (_currentText.isNotEmpty) {
      _controller.document.insert(0, _currentText);
    }

    _focusNode = FocusNode();

    // Initialize animation controller
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // Initialize animations - will be updated in build method with responsive values
    _heightAnimation = Tween<double>(
      begin: widget.compactHeight,
      end: widget.expandedHeight,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _colorAnimation = ColorTween(
      begin: widget.backgroundColor,
      end: widget.hoverColor,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _borderColorAnimation = ColorTween(
      begin: widget.borderColor,
      end: widget.hoverBorderColor,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Add listeners
    _controller.addListener(_handleTextChange);
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _controller.removeListener(_handleTextChange);
    _controller.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// Gets the display text from JSON data or falls back to initial text
  String _getDisplayText() {
    if (widget.jsonData != null) {
      return widget.jsonData![widget.displayKey]?.toString() ??
          widget.initialText;
    }
    return widget.initialText;
  }

  void _handleTextChange() {
    final newText = _controller.document.toPlainText();
    if (_currentText != newText) {
      setState(() {
        _currentText = newText;
      });

      if (widget.onTextChanged != null) {
        widget.onTextChanged!(newText);
      }
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused) {
        _currentMode = RichTextDisplayMode.editing;
      } else if (!_isHovered) {
        _currentMode = RichTextDisplayMode.compact;
        _animationController.reverse();
      }
    });

    if (widget.onFocus != null) {
      widget.onFocus!(_isFocused);
    }
  }

  void _updateResponsiveAnimation(BuildContext context) {
    final toolbarHeight =
        (widget.showToolbar && widget.isEditable && !widget.isDisabled)
            ? 44.0
            : 0.0;
    final responsiveCompactHeight = _getResponsiveHeight(context);
    final responsiveExpandedHeight =
        responsiveCompactHeight * 3.0; // Make expanded height 3x compact height
    final effectiveExpandedHeight =
        responsiveExpandedHeight + toolbarHeight - 26.0;

    _heightAnimation = Tween<double>(
      begin: responsiveCompactHeight,
      end: effectiveExpandedHeight,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
      if (isHovered && _currentMode == RichTextDisplayMode.compact) {
        _currentMode = RichTextDisplayMode.hover;
        _animationController.forward();
      } else if (!isHovered &&
          !_isFocused &&
          _currentMode == RichTextDisplayMode.hover) {
        _currentMode = RichTextDisplayMode.compact;
        _animationController.reverse();
      }
    });

    if (widget.onHover != null) {
      widget.onHover!(isHovered);
    }
  }

  void _onTap() {
    if (!widget.isDisabled && widget.isEditable) {
      _focusNode.requestFocus();
      if (_currentMode == RichTextDisplayMode.compact) {
        setState(() {
          _currentMode = RichTextDisplayMode.editing;
          _animationController.forward();
        });
      }
    }

    if (widget.onTap != null) {
      widget.onTap!();
    }
  }

  void _onExpand() {
    _showExpandedEditor();

    if (widget.onExpand != null) {
      widget.onExpand!();
    }
  }

  void _showExpandedEditor() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _ExpandedQuillEditor(
          controller: _controller,
          onTextChanged: (text) {
            setState(() {
              _currentText = text;
            });
            if (widget.onTextChanged != null) {
              widget.onTextChanged!(text);
            }
          },
          isDarkTheme: widget.isDarkTheme,
          fontSize: widget.fontSize,
          fontFamily: widget.fontFamily,
        );
      },
    );
  }

  Widget _buildSmoothContent() {
    final animationProgress = _animationController.value;

    // Use a smooth transition threshold to prevent flickering
    if (_currentMode == RichTextDisplayMode.compact &&
        animationProgress < 0.2) {
      return _buildCompactContent();
    } else if ((_currentMode == RichTextDisplayMode.hover ||
            _currentMode == RichTextDisplayMode.editing) &&
        animationProgress > 0.8) {
      return _buildHoverContent();
    } else {
      // During transition, use opacity to smoothly fade between content
      return Stack(
        children: [
          if (animationProgress < 0.5)
            Opacity(
              opacity: (1.0 - animationProgress * 2).clamp(0.0, 1.0),
              child: _buildCompactContent(),
            ),
          if (animationProgress > 0.5)
            Opacity(
              opacity: ((animationProgress - 0.5) * 2).clamp(0.0, 1.0),
              child: _buildHoverContent(),
            ),
        ],
      );
    }
  }

  Widget _buildCompactContent() {
    return Padding(
      padding: EdgeInsets.all(8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              _currentText.isEmpty ? 'Type Here' : _currentText,
              // style: TextStyle(
              //   color: widget.isDarkTheme ? Colors.white : Color(0xFF333333),
              //   fontSize: _getResponsiveValueFontSize(context),
              //   fontWeight: widget.fontWeight,
              //   fontFamily: widget.fontFamily,
              // ),
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                fontSize: _getResponsiveValueFontSize(context),
                color: widget.isDarkTheme ? Colors.white : Color(0xFF333333),
              ),
              textAlign: widget.textAlign,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          const SizedBox(width: 4.0),
          Container(
            child: SvgPicture.asset(
              _isHovered
                  ? 'assets/images/edit_icon_hover.svg'
                  : 'assets/images/edit_icon.svg',
              package: 'ui_controls_library',
              width: _getResponsiveIconSize(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHoverContent() {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: TextSelectionTheme(
                data: TextSelectionThemeData(
                  selectionColor: _primaryBlue.withOpacity(0.3),
                  selectionHandleColor: _primaryBlue,
                ),
                child: QuillEditor.basic(
                  controller: _controller,
                  focusNode: _focusNode,
                ),
              ),
            ),
          ),
          if (widget.showToolbar && widget.isEditable && !widget.isDisabled)
            _buildBottomToolbar(),
        ],
      ),
    );
  }

  Widget _buildBottomToolbar() {
    return Container(
      height: 40.0,
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
      decoration: BoxDecoration(
        color: widget.isDarkTheme ? Colors.grey.shade800 : Colors.grey.shade100,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(4.0),
          bottomRight: Radius.circular(4.0),
        ),
        border: Border.all(
          color:
              widget.isDarkTheme ? Colors.grey.shade600 : Colors.grey.shade300,
          width: 0.5,
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Row(
            children: [
              Expanded(
                child: _buildResponsiveToolbarContent(
                  constraints.maxWidth - 40,
                ), // Reserve space for expand button
              ),
              // Always show expand button on the right
              IconButton(
                icon: const Icon(Icons.open_in_full),
                onPressed: _onExpand,
                iconSize: 14.0,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(
                  minWidth: 28.0,
                  minHeight: 28.0,
                ),
                tooltip: 'Expand Editor',
                color:
                    widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
              ),
            ],
          );
        },
      ),
    );
  }

  /// Builds responsive toolbar content that hides icons based on available width
  Widget _buildResponsiveToolbarContent(double availableWidth) {
    // Define icon priorities and approximate widths
    final List<ToolbarItem> allItems = [
      // Priority 1: Essential formatting (always show if possible)
      if (widget.showBoldButton)
        ToolbarItem(
          widget: _buildFormatButton(
            Icons.format_bold,
            imageUrl: 'assets/images/bold.svg',
            Attribute.bold,
            'Bold',
          ),
          width: 32.0,
          priority: 1,
        ),
      if (widget.showItalicButton)
        ToolbarItem(
          widget: _buildFormatButton(
            Icons.format_italic,
            imageUrl: 'assets/images/italic.svg',
            Attribute.italic,
            'Italic',
          ),
          width: 32.0,
          priority: 1,
        ),

      // Priority 2: Secondary formatting
      if (widget.showUnderlineButton)
        ToolbarItem(
          widget: _buildFormatButton(
            Icons.format_underlined,
            imageUrl: 'assets/images/underline.svg',
            Attribute.underline,
            'Underline',
          ),
          width: 32.0,
          priority: 2,
        ),
      if (widget.showStrikethroughButton)
        ToolbarItem(
          widget: _buildFormatButton(
            Icons.strikethrough_s,
            imageUrl: 'assets/images/strike.svg',
            Attribute.strikeThrough,
            'Strikethrough',
          ),
          width: 32.0,
          priority: 2,
        ),

      // Divider
      ToolbarItem(widget: _buildVerticalDivider(), width: 9.0, priority: 2),

      // Priority 3: Font controls
      if (widget.showFontSizePicker)
        ToolbarItem(widget: _buildFontSizeButton(), width: 60.0, priority: 3),

      // Priority 4: Colors
      if (widget.showColorPicker)
        ToolbarItem(widget: _buildColorButton(), width: 36.0, priority: 4),
      ToolbarItem(
        widget: _buildBackgroundColorButton(),
        width: 32.0,
        priority: 4,
      ),

      // Divider
      ToolbarItem(widget: _buildVerticalDivider(), width: 9.0, priority: 4),

      // Priority 5: Alignment (show as group or not at all)
      if (widget.showAlignmentButtons)
        ToolbarItem(
          widget: _buildAllAlignmentButtons(),
          width: 120.0, // 4 buttons * 30px each
          priority: 5,
        ),

      // Priority 6: Lists
      if (widget.showBulletList)
        ToolbarItem(
          widget: _buildFormatButton(
            Icons.format_list_bulleted,
            imageUrl: 'assets/images/list-icon.svg',
            Attribute.ul,
            'Bullet List',
          ),
          width: 32.0,
          priority: 6,
        ),
      if (widget.showNumberedList)
        ToolbarItem(
          widget: _buildFormatButton(
            Icons.format_list_numbered,
            imageUrl: 'assets/images/ol-icon.svg',
            Attribute.ol,
            'Numbered List',
          ),
          width: 32.0,
          priority: 6,
        ),

      // Divider
      ToolbarItem(widget: _buildVerticalDivider(), width: 9.0, priority: 6),

      // Priority 7: Clear formatting (lowest priority)
      if (widget.showClearFormattingButton)
        ToolbarItem(
          widget: _buildClearFormatButton(),
          width: 36.0,
          priority: 7,
        ),
    ];

    // Calculate which items can fit
    final List<Widget> visibleItems = [];
    double usedWidth = 0.0;

    // Sort by priority (lower number = higher priority)
    allItems.sort((a, b) => a.priority.compareTo(b.priority));

    for (final item in allItems) {
      if (usedWidth + item.width <= availableWidth) {
        visibleItems.add(item.widget);
        usedWidth += item.width;
      }
    }

    // If we have very little space, show only the most essential items
    if (availableWidth < 100) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showBoldButton)
            _buildFormatButton(
              Icons.format_bold,
              imageUrl: 'assets/images/bold.svg',
              Attribute.bold,
              'Bold',
            ),
          if (widget.showItalicButton)
            _buildFormatButton(
              Icons.format_italic,
              imageUrl: 'assets/images/italic.svg',
              Attribute.italic,
              'Italic',
            ),
        ],
      );
    }

    return Row(mainAxisSize: MainAxisSize.min, children: visibleItems);
  }

  List<Widget> _buildCompactToolbarButtons() {
    return [
      // Font family dropdown
      _buildFontFamilyButton(),
      _buildVerticalDivider(),

      // Font size dropdown
      if (widget.showFontSizePicker) _buildFontSizeButton(),
      if (widget.showFontSizePicker) _buildVerticalDivider(),

      // Basic formatting buttons
      if (widget.showBoldButton)
        _buildFormatButton(
          Icons.format_bold,
          imageUrl: 'assets/images/bold.svg',
          Attribute.bold,
          'Bold',
        ),

      if (widget.showItalicButton)
        _buildFormatButton(
          Icons.format_italic,
          imageUrl: 'assets/images/italic.svg',
          Attribute.italic,
          'Italic',
        ),

      if (widget.showUnderlineButton)
        _buildFormatButton(
          Icons.format_underlined,
          imageUrl: 'assets/images/underline.svg',
          Attribute.underline,
          'Underline',
        ),

      if (widget.showStrikethroughButton)
        _buildFormatButton(
          Icons.strikethrough_s,
          imageUrl: 'assets/images/strike.svg',
          Attribute.strikeThrough,
          'Strikethrough',
        ),

      _buildVerticalDivider(),

      // Color pickers
      if (widget.showColorPicker) _buildColorButton(),
      _buildBackgroundColorButton(),
      _buildVerticalDivider(),

      // Alignment buttons (all 4 options)
      if (widget.showAlignmentButtons) _buildAllAlignmentButtons(),
      //_buildVerticalDivider(),

      // List buttons
      if (widget.showBulletList)
        _buildFormatButton(
          Icons.format_list_bulleted,
          imageUrl: 'assets/images/list-icon.svg',
          Attribute.ul,
          'Bullet List',
        ),

      if (widget.showNumberedList)
        _buildFormatButton(
          Icons.format_list_numbered,
          imageUrl: 'assets/images/ol-icon.svg',
          Attribute.ol,
          'Numbered List',
        ),

      _buildVerticalDivider(),

      // Insert options
      //_buildInsertTableButton(),
      if (widget.showLinkButton)
        //_buildInsertLinkButton(),
        //_buildInsertAttachmentButton(),
        //_buildVerticalDivider(),
        // Clear formatting
        if (widget.showClearFormattingButton) _buildClearFormatButton(),

      const Spacer(),

      // Expand button
      IconButton(
        icon: const Icon(Icons.open_in_full),
        onPressed: _onExpand,
        iconSize: 14.0,
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(minWidth: 28.0, minHeight: 28.0),
        tooltip: 'Expand Editor',
        color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
      ),
    ];
  }

  List<Widget> _buildFullToolbarButtons() {
    return [
      // Font size dropdown
      if (widget.showFontSizePicker) _buildFontSizeButton(),
      if (widget.showFontSizePicker) _buildVerticalDivider(),

      //Basic formatting buttons
      if (widget.showBoldButton)
        _buildFormatButton(
          Icons.format_bold,
          imageUrl: 'assets/images/bold.svg',
          Attribute.bold,
          'Bold',
        ),

      if (widget.showItalicButton)
        _buildFormatButton(
          Icons.format_italic,
          imageUrl: 'assets/images/italic.svg',
          Attribute.italic,
          'Italic',
        ),

      if (widget.showUnderlineButton)
        _buildFormatButton(
          Icons.format_underlined,
          imageUrl: 'assets/images/underline.svg',
          Attribute.underline,
          'Underline',
        ),

      if (widget.showStrikethroughButton)
        _buildFormatButton(
          Icons.strikethrough_s,
          imageUrl: 'assets/images/strike.svg',
          Attribute.strikeThrough,
          'Strikethrough',
        ),

      if (widget.showBoldButton ||
          widget.showItalicButton ||
          widget.showUnderlineButton ||
          widget.showStrikethroughButton)
        _buildVerticalDivider(),

      //Color picker
      if (widget.showColorPicker) _buildColorButton(),

      if (widget.showColorPicker) _buildVerticalDivider(),

      // List buttons
      if (widget.showBulletList)
        _buildFormatButton(
          Icons.format_list_bulleted,
          imageUrl: 'assets/images/list-icon.svg',
          Attribute.ul,
          'Bullet List',
        ),

      if (widget.showNumberedList)
        _buildFormatButton(
          Icons.format_list_numbered,
          imageUrl: 'assets/images/ol-icon.svg',
          Attribute.ol,
          'Numbered List',
        ),

      if (widget.showBulletList || widget.showNumberedList)
        _buildVerticalDivider(),

      // Clear formatting
      if (widget.showClearFormattingButton) _buildClearFormatButton(),
      const Spacer(),

      // Expand button
      IconButton(
        icon: const Icon(Icons.open_in_full),
        onPressed: _onExpand,
        iconSize: 14.0,
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(minWidth: 28.0, minHeight: 28.0),
        tooltip: 'Expand Editor',
        color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
      ),
    ];
  }

  Widget _buildAlignmentButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildFormatButton(
          Icons.format_align_left,
          imageUrl: 'assets/images/align-left.svg',
          Attribute.leftAlignment,
          'Align Left',
        ),
        _buildFormatButton(
          Icons.format_align_center,
          imageUrl: 'assets/images/align-center.svg',
          Attribute.centerAlignment,
          'Align Center',
        ),
        _buildFormatButton(
          Icons.format_align_right,
          imageUrl: 'assets/images/align-right.svg',
          Attribute.rightAlignment,
          'Align Right',
        ),
      ],
    );
  }

  Widget _buildFormatButton(
    IconData icon,
    Attribute attribute,
    String tooltip, {
    String? imageUrl,
  }) {
    final isActive = _controller.getSelectionStyle().containsKey(attribute.key);

    return Container(
      decoration: BoxDecoration(
        color:
            isActive
                ? (widget.isDarkTheme
                    ? widget.activeButtonBackgroundColor.withOpacity(0.2)
                    : widget.activeButtonBackgroundColor)
                : Colors.transparent,
        borderRadius: BorderRadius.circular(widget.activeButtonBorderRadius),
      ),
      child: IconButton(
        icon:
            imageUrl != null && imageUrl.isNotEmpty
                ? SvgPicture.asset(
                  imageUrl,
                  package: 'ui_controls_library',
                  width: _getResponsiveIconSizeSmall(context),
                  colorFilter: ColorFilter.mode(
                    isActive
                        ? widget.activeButtonIconColor
                        : (widget.isDarkTheme
                            ? Colors.white70
                            : Colors.grey.shade700),
                    BlendMode.srcIn,
                  ),
                )
                : Icon(icon),
        onPressed: () {
          if (!isActive) {
            // If the attribute is not active, apply it
            _controller.formatSelection(attribute);
          } else {
            // If the attribute is already active, remove it
            _controller.formatSelection(Attribute.clone(attribute, null));
          }
          // _controller.formatSelection(attribute);
        },
        color:
            isActive
                ? widget.activeButtonIconColor
                : (widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700),
        iconSize: 16.0,
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(minWidth: 28.0, minHeight: 28.0),
        tooltip: tooltip,
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(Colors.transparent),
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          splashFactory: NoSplash.splashFactory,
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                widget.activeButtonBorderRadius,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildColorButton() {
    final screenHeight = MediaQuery.of(context).size.height;
    final offsetY = screenHeight < 600 ? 6.0 : 10.0;
    return PopupMenuButton<Color>(
      offset: Offset(0, offsetY), // Responsive offset
      icon: Stack(
        children: [
          SvgPicture.asset(
            'assets/images/text-color.svg',
            package: 'ui_controls_library',
            width: _getResponsiveIconSizeSmall(context),
          ),
          // Icon(
          //   Icons.format_color_text,
          //   size: 14.0,
          //   color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
          // ),
          // Positioned(
          //   bottom: 2,
          //   left: 2,
          //   right: 2,
          //   child: Container(
          //     height: 3.0,
          //     decoration: BoxDecoration(
          //       color: Colors.black,
          //       borderRadius: BorderRadius.circular(1.0),
          //     ),
          //   ),
          // ),
        ],
      ),
      tooltip: 'Text Color',
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(minWidth: 32.0, minHeight: 32.0),
      splashRadius: 0.1,
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        splashFactory: NoSplash.splashFactory,
        shadowColor: WidgetStateProperty.all(Colors.transparent),
        surfaceTintColor: WidgetStateProperty.all(Colors.transparent),
      ),
      itemBuilder:
          (context) => [
            // PopupMenuItem(
            //   value: Colors.black,
            //   child: _ColorItem(color: Colors.black, name: 'Black'),
            // ),
            PopupMenuItem(
              value: Colors.black,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.black,
                name: 'Black',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.red,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.red,
                name: 'Red',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Color(0xFF0058FF),
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Color(0xFF0058FF),
                name: 'Blue',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.green,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.green,
                name: 'Green',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.orange,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.orange,
                name: 'Orange',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.purple,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.purple,
                name: 'Purple',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
          ],
      onSelected: (color) {
        _controller.formatSelection(
          ColorAttribute(color.value.toRadixString(16)),
        );
      },
    );
  }

  Widget _buildClearFormatButton() {
    return IconButton(
      icon: const Icon(Icons.format_clear),
      onPressed: () {
        final selection = _controller.selection;
        if (selection.isValid) {
          // Clear all formatting for selected text
          _controller.formatSelection(Attribute.clone(Attribute.bold, null));
          _controller.formatSelection(Attribute.clone(Attribute.italic, null));
          _controller.formatSelection(
            Attribute.clone(Attribute.underline, null),
          );
          _controller.formatSelection(
            Attribute.clone(Attribute.strikeThrough, null),
          );
        }
      },
      iconSize: 14.0,
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(minWidth: 32.0, minHeight: 32.0),
      tooltip: 'Clear Formatting',
      color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        splashFactory: NoSplash.splashFactory,
      ),
    );
  }

  Widget _buildVerticalDivider() {
    return Container(
      width: 1.0,
      height: 20.0,
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      color: widget.isDarkTheme ? Colors.grey.shade600 : Colors.grey.shade400,
    );
  }

  Widget _buildFontSizeButton() {
    final fontSizes = [10, 12, 14, 16, 18, 20, 24, 28, 32];
    final screenHeight = MediaQuery.of(context).size.height;
    final offsetY = screenHeight < 600 ? 6.0 : 10.0;
    return PopupMenuButton<int>(
      //offset: const Offset(0, 28), // Position dropdown relative to button
      offset: Offset(0, offsetY), // Responsive offset
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 0.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _controller
                      .getSelectionStyle()
                      .attributes[Attribute.size.key]
                      ?.value
                      ?.toString() ??
                  '14',
              style: TextStyle(
                fontSize: 12.0,
                color:
                    widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              size: 16.0,
              color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
            ),
          ],
        ),
      ),
      tooltip: 'Font Size',
      position:
          PopupMenuPosition.under, // Ensure dropdown appears under the button
      constraints: BoxConstraints(
        maxHeight: 150.0, // Maximum height of 150px before scrolling
        minWidth: 100.0,
      ),
      itemBuilder: (context) {
        return fontSizes
            .map(
              (size) => PopupMenuItem<int>(
                value: size,
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                height: 32,
                child: Text(
                  size.toString(),
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    color:
                        widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
                    fontSize: _getResponsiveValueFontSize(context),
                  ),
                ),
              ),
            )
            .toList();
      },
      onSelected: (size) {
        _controller.formatSelection(
          Attribute.fromKeyValue('size', size.toString()),
        );
      },
      padding: EdgeInsets.zero,
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        splashFactory: NoSplash.splashFactory,
      ),
    );
  }

  Widget _buildFontFamilyButton() {
    final fontFamilies = [
      'Arial',
      'Times New Roman',
      'Helvetica',
      'Georgia',
      'Verdana',
    ];
    final currentFontFamily =
        _controller
            .getSelectionStyle()
            .attributes['font-family']
            ?.value
            ?.toString() ??
        'Arial';
    final screenHeight = MediaQuery.of(context).size.height;
    final offsetY = screenHeight < 600 ? 6.0 : 10.0;
    return PopupMenuButton<String>(
      offset: Offset(0, offsetY), // Responsive offset
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 0.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              currentFontFamily,
              style: TextStyle(
                fontSize: 12.0,
                fontFamily: currentFontFamily,
                color:
                    widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 2.0),
            Icon(
              Icons.arrow_drop_down,
              size: 14.0,
              color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
            ),
          ],
        ),
      ),
      tooltip: 'Font Family',
      position:
          PopupMenuPosition.under, // Ensure dropdown appears under the button
      constraints: BoxConstraints(
        maxHeight: 150.0, // Maximum height of 150px before scrolling
        minWidth: 100.0,
      ),

      itemBuilder:
          (context) =>
              fontFamilies
                  .map(
                    (family) => PopupMenuItem<String>(
                      value: family,
                      child: Text(family, style: TextStyle(fontFamily: family)),
                    ),
                  )
                  .toList(),
      onSelected: (fontFamily) {
        // Apply font family to selected text using a custom attribute
        _controller.formatSelection(
          Attribute('font-family', AttributeScope.inline, fontFamily),
        );
      },
    );
  }

  Widget _buildBackgroundColorButton() {
    return PopupMenuButton<Color>(
      icon: Stack(
        children: [
          SvgPicture.asset(
            'assets/images/background-color.svg',
            package: 'ui_controls_library',
            width: _getResponsiveIconSizeSmall(context),
          ),
          // Icon(
          //   Icons.format_color_fill,
          //   size: 14.0,
          //   color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
          // ),
          // Positioned(
          //   bottom: 2,
          //   left: 2,
          //   right: 2,
          //   child: Container(
          //     height: 3.0,
          //     decoration: BoxDecoration(
          //       color: Colors.yellow,
          //       borderRadius: BorderRadius.circular(1.0),
          //     ),
          //   ),
          // ),
        ],
      ),
      tooltip: 'Background Color',
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(minWidth: 28.0, minHeight: 28.0),
      splashRadius: 0.1,
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        splashFactory: NoSplash.splashFactory,
        shadowColor: WidgetStateProperty.all(Colors.transparent),
        surfaceTintColor: WidgetStateProperty.all(Colors.transparent),
      ),
      itemBuilder:
          (context) => [
            // PopupMenuItem(
            //   value: Colors.transparent,
            //   child: _ColorItem(color: Colors.transparent, name: 'None'),
            // ),
            PopupMenuItem(
              value: Colors.transparent,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.transparent,
                name: 'None',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.yellow,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.yellow,
                name: 'Yellow',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.lightBlue,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.lightBlue,
                name: 'Light Blue',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.lightGreen,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.lightGreen,
                name: 'Light Green',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.pink.shade100,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.pink.shade100,
                name: 'Light Pink',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
          ],
      onSelected: (color) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Background color functionality coming soon!'),
          ),
        );
      },
    );
  }

  Widget _buildAllAlignmentButtons() {
    // Get current alignment from selection style
    final style = _controller.getSelectionStyle();
    String? alignment =
        style.attributes[Attribute.leftAlignment.key]?.value as String? ??
        style.attributes[Attribute.centerAlignment.key]?.value as String? ??
        style.attributes[Attribute.rightAlignment.key]?.value as String? ??
        style.attributes[Attribute.justifyAlignment.key]?.value as String?;

    Widget buildAlignmentButton(
      IconData icon,
      String imageUrl,
      Attribute attribute,
      String tooltip,
      String alignKey,
    ) {
      final isActive = alignment == alignKey;
      return Container(
        decoration: BoxDecoration(
          color:
              isActive
                  ? (widget.isDarkTheme
                      ? Colors.blue.shade700
                      : widget.activeButtonBackgroundColor)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(widget.activeButtonBorderRadius),
        ),
        child: IconButton(
          icon: SvgPicture.asset(
            imageUrl,
            package: 'ui_controls_library',
            width: _getResponsiveIconSizeSmall(context),
            colorFilter: ColorFilter.mode(
              isActive
                  ? Color(0xFF0058FF)
                  : (widget.isDarkTheme
                      ? Colors.white70
                      : Colors.grey.shade700),
              BlendMode.srcIn,
            ),
          ),
          onPressed: () {
            _controller.formatSelection(attribute);
          },
          color:
              isActive
                  ? Color(0xFF0058FF)
                  : (widget.isDarkTheme
                      ? Colors.white70
                      : Colors.grey.shade700),
          iconSize: 16.0,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(minWidth: 28.0, minHeight: 28.0),
          tooltip: tooltip,
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            splashFactory: NoSplash.splashFactory,
            shape: WidgetStateProperty.all(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  widget.activeButtonBorderRadius,
                ),
              ),
            ),
          ),
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        buildAlignmentButton(
          Icons.format_align_left,
          'assets/images/align-left.svg',
          Attribute.leftAlignment,
          'Align Left',
          'left',
        ),
        buildAlignmentButton(
          Icons.format_align_center,
          'assets/images/align-center.svg',
          Attribute.centerAlignment,
          'Align Center',
          'center',
        ),
        buildAlignmentButton(
          Icons.format_align_right,
          'assets/images/align-right.svg',
          Attribute.rightAlignment,
          'Align Right',
          'right',
        ),
        buildAlignmentButton(
          Icons.format_align_justify,
          'assets/images/justify.svg',
          Attribute.justifyAlignment,
          'Justify',
          'justify',
        ),
      ],
    );
  }

  // Widget _buildInsertTableButton() {
  //   return IconButton(
  //     //icon: const Icon(Icons.table_chart),
  //     icon: SvgPicture.asset(
  //             'assets/images/icon-table.svg',
  //             package: 'ui_controls_library',
  //             width: _getResponsiveIconSizeSmall(context),
  //     ),
  //     onPressed: () {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         const SnackBar(content: Text('Table functionality coming soon!')),
  //       );
  //     },
  //     iconSize: 14.0,
  //     padding: EdgeInsets.zero,
  //     constraints: const BoxConstraints(minWidth: 28.0, minHeight: 28.0),
  //     tooltip: 'Insert Table',
  //     color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
  //     style: ButtonStyle(
  //       backgroundColor: WidgetStateProperty.all(Colors.transparent),
  //       overlayColor: WidgetStateProperty.all(Colors.transparent),
  //       splashFactory: NoSplash.splashFactory,
  //     ),
  //   );
  // }

  // Widget _buildInsertLinkButton() {
  //   return IconButton(
  //     icon: const Icon(Icons.link),
  //     onPressed: () {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         const SnackBar(content: Text('Link functionality coming soon!')),
  //       );
  //     },
  //     iconSize: 14.0,
  //     padding: EdgeInsets.zero,
  //     constraints: const BoxConstraints(minWidth: 28.0, minHeight: 28.0),
  //     tooltip: 'Insert Link',
  //     color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
  //     style: ButtonStyle(
  //       backgroundColor: WidgetStateProperty.all(Colors.transparent),
  //       overlayColor: WidgetStateProperty.all(Colors.transparent),
  //       splashFactory: NoSplash.splashFactory,
  //     ),
  //   );
  // }

  // Widget _buildInsertAttachmentButton() {
  //   return IconButton(
  //     //icon: const Icon(Icons.attach_file),
  //     icon: SvgPicture.asset(
  //             'assets/images/attach.svg',
  //             package: 'ui_controls_library',
  //             width: _getResponsiveIconSizeSmall(context),
  //     ),
  //     onPressed: () {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         const SnackBar(content: Text('File attachment coming soon!')),
  //       );
  //     },
  //     iconSize: 14.0,
  //     padding: EdgeInsets.zero,
  //     constraints: const BoxConstraints(minWidth: 28.0, minHeight: 28.0),
  //     tooltip: 'Attach File',
  //     color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
  //     style: ButtonStyle(
  //       backgroundColor: WidgetStateProperty.all(Colors.transparent),
  //       overlayColor: WidgetStateProperty.all(Colors.transparent),
  //       splashFactory: NoSplash.splashFactory,
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    // Update animation with responsive values
    _updateResponsiveAnimation(context);

    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade900 : widget.backgroundColor;
    final effectiveBorderColor =
        widget.isDarkTheme ? Colors.grey.shade600 : widget.borderColor;
    final effectiveShadowColor =
        widget.isDarkTheme ? Colors.black : Colors.black26;

    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: GestureDetector(
        onTap: _onTap,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            // Calculate smooth height transition to prevent gaps
            // Always use animation value during transitions to prevent space issues
            final responsiveCompactHeight = _getResponsiveHeight(context);
            final currentHeight =
                _animationController.isAnimating
                    ? _heightAnimation.value
                    : (_currentMode == RichTextDisplayMode.compact
                        ? responsiveCompactHeight
                        : _heightAnimation.value);

            return Container(
              width: widget.width,
              height: currentHeight,
              margin: widget.margin,
              clipBehavior: Clip.hardEdge, // Prevent overflow
              decoration: BoxDecoration(
                //color: _colorAnimation.value ?? effectiveBackgroundColor,
                color: widget.backgroundColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _borderColorAnimation.value ??
                              effectiveBorderColor,
                          width: widget.borderWidth,
                        )
                        : null,
                boxShadow:
                    widget.hasShadow
                        ? [
                          BoxShadow(
                            color: effectiveShadowColor.withAlpha(26),
                            blurRadius: widget.elevation,
                            offset: const Offset(0, 2),
                          ),
                        ]
                        : null,
              ),
              child: Padding(
                //padding: EdgeInsets.symmetric(horizontal: 16.0),
                //padding: _getResponsivePadding(context),
                padding: EdgeInsets.all(0),
                child: _buildSmoothContent(),
              ),
            );
          },
        ),
      ),
    );
  }
}

/// Expanded Quill editor dialog
class _ExpandedQuillEditor extends StatefulWidget {
  final QuillController controller;
  final Function(String) onTextChanged;
  final bool isDarkTheme;
  final double fontSize;
  final String? fontFamily;

  const _ExpandedQuillEditor({
    required this.controller,
    required this.onTextChanged,
    this.isDarkTheme = false,
    this.fontSize = 16.0,
    this.fontFamily,
  });

  @override
  State<_ExpandedQuillEditor> createState() => _ExpandedQuillEditorState();
}

class _ExpandedQuillEditorState extends State<_ExpandedQuillEditor> {
  late FocusNode _focusNode;
  int _wordCount = 0;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _updateWordCount();
    widget.controller.addListener(_handleTextChange);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_handleTextChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _handleTextChange() {
    final newText = widget.controller.document.toPlainText();
    setState(() {
      _updateWordCount();
    });
    widget.onTextChanged(newText);
  }

  void _updateWordCount() {
    final text = widget.controller.document.toPlainText();
    _wordCount =
        text.trim().isEmpty ? 0 : text.trim().split(RegExp(r'\s+')).length;
  }

  Widget _buildExpandedToolbarButton(
    IconData icon,
    String tooltip,
    VoidCallback onPressed, {
    String? imageUrl,
  }) {
    return IconButton(
      icon:
          imageUrl != null && imageUrl.isNotEmpty
              ? SvgPicture.asset(
                imageUrl,
                package: 'ui_controls_library',
                width: _getResponsiveIconSize(context),
              )
              : Icon(icon),
      onPressed: onPressed,
      iconSize: 18.0,
      //padding: EdgeInsets.zero,
      //constraints: const BoxConstraints(minWidth: 32.0, minHeight: 32.0),
      tooltip: tooltip,
      color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        splashFactory: NoSplash.splashFactory,
      ),
    );
  }

  Widget _buildExpandedFormatButton(
    IconData icon,
    Attribute attribute,
    String tooltip, {
    String? imageUrl,
    String? key,
    bool requiresKey = false,
  }) {
    bool isActive = false;
    if (requiresKey) {
      final style = widget.controller.getSelectionStyle();
      String? alignment =
          style.attributes[Attribute.leftAlignment.key]?.value as String? ??
          style.attributes[Attribute.centerAlignment.key]?.value as String? ??
          style.attributes[Attribute.rightAlignment.key]?.value as String? ??
          style.attributes[Attribute.justifyAlignment.key]?.value as String?;
      isActive = alignment == key;
    } else {
      isActive = widget.controller.getSelectionStyle().containsKey(
        attribute.key,
      );
    }

    return Container(
      decoration: BoxDecoration(
        color:
            isActive
                ? (widget.isDarkTheme
                    ? const Color(0xFFE3F2FD).withOpacity(0.2)
                    : const Color(0xFFE3F2FD))
                : Colors.transparent,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: IconButton(
        icon:
            imageUrl != null && imageUrl.isNotEmpty
                ? SvgPicture.asset(
                  imageUrl,
                  package: 'ui_controls_library',
                  width: _getResponsiveIconSize(context),
                  colorFilter: ColorFilter.mode(
                    isActive
                        ? const Color(0xFF0058FF)
                        : (widget.isDarkTheme
                            ? Colors.white70
                            : Colors.grey.shade700),
                    BlendMode.srcIn,
                  ),
                )
                : Icon(icon),
        onPressed: () {
          if (!isActive) {
            // If the attribute is not active, apply it
            widget.controller.formatSelection(attribute);
          } else {
            // If the attribute is already active, remove it
            widget.controller.formatSelection(Attribute.clone(attribute, null));
          }
          // widget.controller.formatSelection(attribute);
        },
        color:
            isActive
                ? const Color(0xFF0058FF)
                : (widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700),
        iconSize: 18.0,
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(minWidth: 32.0, minHeight: 32.0),
        tooltip: tooltip,
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(Colors.transparent),
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          splashFactory: NoSplash.splashFactory,
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
          ),
        ),
      ),
    );
  }

  Widget _buildExpandedVerticalDivider() {
    return Container(
      width: 1.0,
      height: 24.0,
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      color: widget.isDarkTheme ? Colors.grey.shade600 : Colors.grey.shade400,
    );
  }

  Widget _buildExpandedFontFamilyButton() {
    final fontFamilies = [
      'Arial',
      'Times New Roman',
      'Helvetica',
      'Georgia',
      'Verdana',
    ];
    final currentFontFamily =
        widget.controller
            .getSelectionStyle()
            .attributes['font-family']
            ?.value
            ?.toString() ??
        'Arial';
    final screenHeight = MediaQuery.of(context).size.height;
    final offsetY = screenHeight < 600 ? 8.0 : 15.0;
    return PopupMenuButton<String>(
      offset: Offset(0, offsetY), // Responsive offset
      position:
          PopupMenuPosition.under, // Ensure dropdown appears under the button
      constraints: BoxConstraints(
        maxHeight: 150.0, // Maximum height of 70px before scrolling
        minWidth: 100.0,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              currentFontFamily,
              style: TextStyle(
                fontSize: 14.0,
                fontFamily: currentFontFamily,
                color:
                    widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 4.0),
            Icon(
              Icons.arrow_drop_down,
              size: 16.0,
              color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
            ),
          ],
        ),
      ),
      tooltip: 'Font Family',
      itemBuilder:
          (context) =>
              fontFamilies
                  .map(
                    (family) => PopupMenuItem<String>(
                      value: family,
                      height: 32, // Compact!
                      padding: EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 0,
                      ), // No default padding!
                      child: Text(
                        family,
                        //style: TextStyle(fontFamily: family)
                        style: FontManager.getCustomStyle(
                          fontFamily: family,
                          fontWeight: FontManager.medium,
                          color:
                              widget.isDarkTheme
                                  ? Colors.white70
                                  : Color(0xFF333333),
                          fontSize: _getResponsiveValueFontSize(context),
                        ),
                      ),
                    ),
                  )
                  .toList(),
      onSelected: (fontFamily) {
        widget.controller.formatSelection(
          Attribute('font-family', AttributeScope.inline, fontFamily),
        );
      },
    );
  }

  Widget _buildExpandedFontSizeButton() {
    final screenHeight = MediaQuery.of(context).size.height;
    final offsetY = screenHeight < 600 ? 8.0 : 15.0;
    return PopupMenuButton<int>(
      //offset: const Offset(0, 36), // Position dropdown relative to button
      offset: Offset(0, offsetY), // Responsive offset
      position:
          PopupMenuPosition.under, // Ensure dropdown appears under the button
      constraints: BoxConstraints(
        maxHeight: 150.0, // Maximum height of 70px before scrolling
        minWidth: 100.0,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),

        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '16',
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
                fontSize: _getResponsiveValueFontSize(context),
              ),
            ),
            const SizedBox(width: 4.0),
            Icon(
              Icons.arrow_drop_down,
              size: 16.0,
              color: widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
            ),
          ],
        ),
      ),
      tooltip: 'Font Size',
      itemBuilder:
          (context) => [
            PopupMenuItem(
              height: 32, // Compact!
              padding: EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 0,
              ), // No default padding!
              value: 8,
              child: Text(
                '8',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                  color:
                      widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
                ),
              ),
            ),
            PopupMenuItem(
              height: 32, // Compact!
              value: 10,
              padding: EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 0,
              ), // No default padding!
              child: Text(
                '10',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                  color:
                      widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
                ),
              ),
            ),
            PopupMenuItem(
              height: 32, // Compact!
              value: 12,
              child: Text(
                '12',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                  color:
                      widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
                ),
              ),
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            ),
            PopupMenuItem(
              height: 32, // Compact!
              padding: EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 0,
              ), // No default padding!
              value: 14,
              child: Text(
                '14',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                  color:
                      widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
                ),
              ),
            ),
            PopupMenuItem(
              height: 32, // Compact!
              padding: EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 0,
              ), // No default padding!
              value: 16,
              child: Text(
                '16',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                  color:
                      widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
                ),
              ),
            ),
            PopupMenuItem(
              height: 32, // Compact!
              padding: EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 0,
              ), // No default padding!
              value: 18,
              child: Text(
                '18',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                  color:
                      widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
                ),
              ),
            ),
            PopupMenuItem(
              height: 32, // Compact!
              padding: EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 0,
              ), // No default padding!
              value: 20,
              child: Text(
                '20',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                  color:
                      widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
                ),
              ),
            ),
            PopupMenuItem(
              height: 32, // Compact!
              padding: EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 0,
              ), // No default padding!
              value: 24,
              child: Text(
                '24',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                  color:
                      widget.isDarkTheme ? Colors.white70 : Color(0xFF333333),
                ),
              ),
            ),
            PopupMenuItem(
              height: 32, // Compact!
              padding: EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 0,
              ), // No default padding!
              value: 28,
              child: Text(
                '28',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              height: 32, // Compact!
              padding: EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 0,
              ), // No default padding!
              value: 32,
              child: Text(
                '32',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
          ],
      onSelected: (fontSize) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Font size: $fontSize (coming soon!)',
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                fontSize: _getResponsiveValueFontSize(context),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildExpandedColorButton() {
    return PopupMenuButton<Color>(
      icon: Stack(
        children: [
          SvgPicture.asset(
            'assets/images/text-color.svg',
            package: 'ui_controls_library',
            width: _getResponsiveIconSize(context),
          ),
          // Icon(
          //   Icons.format_color_text,
          //   size: 18.0,
          //   color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
          // ),
          // Positioned(
          //   bottom: 2,
          //   left: 2,
          //   right: 2,
          //   child: Container(
          //     height: 3.0,
          //     decoration: BoxDecoration(
          //       color: Colors.black,
          //       borderRadius: BorderRadius.circular(1.0),
          //     ),
          //   ),
          // ),
        ],
      ),
      tooltip: 'Text Color',
      //padding: EdgeInsets.zero,
      //constraints: const BoxConstraints(minWidth: 32.0, minHeight: 32.0),
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        splashFactory: NoSplash.splashFactory,
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
        ),
      ),
      itemBuilder:
          (context) => [
            // PopupMenuItem(
            //   value: Colors.black,
            //   child: _ColorItem(color: Colors.black, name: 'Black'),
            //   height: 32, // Compact!
            //   padding: EdgeInsets.symmetric(
            //     horizontal: 8,
            //     vertical: 0,
            //   ), // No default padding!
            // ),
            PopupMenuItem(
              value: Colors.black,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.black,
                name: 'Black',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.red,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.red,
                name: 'Red',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.blue,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.blue,
                name: 'Blue',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.green,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.green,
                name: 'Green',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.orange,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.orange,
                name: 'Orange',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.purple,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.purple,
                name: 'Purple',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
          ],
      onSelected: (color) {
        widget.controller.formatSelection(
          ColorAttribute(color.value.toRadixString(16)),
        );
      },
    );
  }

  Widget _buildExpandedBackgroundColorButton() {
    return PopupMenuButton<Color>(
      icon: Stack(
        children: [
          SvgPicture.asset(
            'assets/images/background-color.svg',
            package: 'ui_controls_library',
            width: _getResponsiveIconSize(context),
          ),
          // Icon(
          //   Icons.format_color_fill,
          //   size: 18.0,
          //   color: widget.isDarkTheme ? Colors.white70 : Colors.grey.shade700,
          // ),
          // Positioned(
          //   bottom: 2,
          //   left: 2,
          //   right: 2,
          //   child: Container(
          //     height: 3.0,
          //     decoration: BoxDecoration(
          //       color: Colors.yellow,
          //       borderRadius: BorderRadius.circular(1.0),
          //     ),
          //   ),
          // ),
        ],
      ),
      tooltip: 'Background Color',
      //padding: EdgeInsets.zero,
      //constraints: const BoxConstraints(minWidth: 28.0, minHeight: 28.0),
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        splashFactory: NoSplash.splashFactory,
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
        ),
      ),
      itemBuilder:
          (context) => [
            // PopupMenuItem(
            //   value: Colors.transparent,
            //   child: _ColorItem(color: Colors.transparent, name: 'None'),
            // ),
            PopupMenuItem(
              value: Colors.transparent,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.transparent,
                name: 'None',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.yellow,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.yellow,
                name: 'Yellow',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.lightBlue,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.lightBlue,
                name: 'Light Blue',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.lightGreen,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.lightGreen,
                name: 'Light Green',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
            PopupMenuItem(
              value: Colors.pink.shade100,
              height: 32,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: _ColorItem(
                color: Colors.pink.shade100,
                name: 'Light Pink',
                textStyle: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.medium,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
            ),
          ],
      onSelected: (color) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Background color functionality coming soon!'),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(16.0),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: widget.isDarkTheme ? Colors.grey.shade900 : Colors.white,
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Column(
          children: [
            // Comprehensive Toolbar
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(
                vertical: 8.0,
                horizontal: 16.0,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(4.0),
                  topRight: Radius.circular(4.0),
                ),
                color:
                    widget.isDarkTheme
                        ? Colors.grey.shade800
                        : Color(0xFFEBEBEB),
                border: Border(
                  bottom: BorderSide(
                    color:
                        widget.isDarkTheme
                            ? Colors.grey.shade600
                            : Color(0xFFCCCCCC),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Wrap(
                      spacing: 4.0,
                      runSpacing: 4.0,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        // Undo/Redo
                        _buildExpandedToolbarButton(
                          Icons.undo,
                          imageUrl: 'assets/images/undo.svg',
                          'Undo',
                          () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Undo functionality coming soon!',
                                ),
                              ),
                            );
                          },
                        ),
                        _buildExpandedToolbarButton(
                          Icons.redo,
                          imageUrl: 'assets/images/redo.svg',
                          'Redo',
                          () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Redo functionality coming soon!',
                                ),
                              ),
                            );
                          },
                        ),
                        //_buildExpandedVerticalDivider(),

                        // Font family dropdown
                        _buildExpandedFontFamilyButton(),
                        //_buildExpandedVerticalDivider(),

                        // Font size dropdown
                        _buildExpandedFontSizeButton(),
                        //_buildExpandedVerticalDivider(),

                        // Basic formatting
                        _buildExpandedFormatButton(
                          Icons.format_bold,
                          Attribute.bold,
                          'Bold',
                          //imageUrl: 'assets/images/list-icon.svg',
                          imageUrl: 'assets/images/bold.svg',
                          key: 'fbold',
                        ),
                        _buildExpandedFormatButton(
                          Icons.format_italic,
                          Attribute.italic,
                          'Italic',
                          imageUrl: 'assets/images/italic.svg',
                          key: 'italic',
                        ),
                        _buildExpandedFormatButton(
                          Icons.format_underlined,
                          Attribute.underline,
                          'Underline',
                          imageUrl: 'assets/images/underline.svg',
                          key: 'underline',
                        ),
                        _buildExpandedFormatButton(
                          Icons.strikethrough_s,
                          Attribute.strikeThrough,
                          'Strikethrough',
                          imageUrl: 'assets/images/strike.svg',
                          key: 'strike',
                        ),
                        //_buildExpandedVerticalDivider(),

                        // Colors
                        _buildExpandedColorButton(),
                        _buildExpandedBackgroundColorButton(),
                        _buildExpandedVerticalDivider(),

                        // Alignment
                        _buildExpandedFormatButton(
                          Icons.format_align_left,
                          Attribute.leftAlignment,
                          'Align Left',
                          imageUrl: 'assets/images/align-left.svg',
                          key: 'left',
                          requiresKey: true,
                        ),
                        _buildExpandedFormatButton(
                          Icons.format_align_center,
                          Attribute.centerAlignment,
                          'Align Center',
                          imageUrl: 'assets/images/align-center.svg',
                          key: 'center',
                          requiresKey: true,
                        ),
                        _buildExpandedFormatButton(
                          Icons.format_align_right,
                          Attribute.rightAlignment,
                          'Align Right',
                          imageUrl: 'assets/images/align-right.svg',
                          key: 'right',
                          requiresKey: true,
                        ),
                        _buildExpandedFormatButton(
                          Icons.format_align_justify,
                          Attribute.justifyAlignment,
                          'Justify',
                          imageUrl: 'assets/images/justify.svg',
                          key: 'justify',
                          requiresKey: true,
                        ),
                        //_buildExpandedVerticalDivider(),

                        // Lists
                        _buildExpandedFormatButton(
                          Icons.format_list_bulleted,
                          Attribute.ul,
                          'Bullet List',
                          imageUrl: 'assets/images/list-icon.svg',
                        ),
                        _buildExpandedFormatButton(
                          Icons.format_list_numbered,
                          Attribute.ol,
                          'Numbered List',
                          imageUrl: 'assets/images/ol-icon.svg',
                        ),
                        _buildExpandedVerticalDivider(),

                        // Insert options
                        // _buildExpandedToolbarButton(
                        //   Icons.table_chart,
                        //   imageUrl: 'assets/images/icon-table.svg',
                        //   'Insert Table',
                        //   () {
                        //     ScaffoldMessenger.of(context).showSnackBar(
                        //       const SnackBar(
                        //         content: Text(
                        //           'Table functionality coming soon!',
                        //         ),
                        //       ),
                        //     );
                        //   },
                        // ),
                        // _buildExpandedToolbarButton(
                        //   Icons.link,

                        //   'Insert Link',
                        //   () {
                        //     ScaffoldMessenger.of(context).showSnackBar(
                        //       const SnackBar(
                        //         content: Text(
                        //           'Link functionality coming soon!',
                        //         ),
                        //       ),
                        //     );
                        //   },
                        // ),
                        // _buildExpandedToolbarButton(
                        //   Icons.attach_file,
                        //   imageUrl: 'assets/images/attach.svg',
                        //   'Attach File',
                        //   () {
                        //     ScaffoldMessenger.of(context).showSnackBar(
                        //       const SnackBar(
                        //         content: Text('File attachment coming soon!'),
                        //       ),
                        //     );
                        //   },
                        // ),
                        //_buildExpandedVerticalDivider(),

                        // Clear formatting
                        _buildExpandedToolbarButton(
                          Icons.format_clear,
                          'Clear Formatting',
                          () {
                            final selection = widget.controller.selection;
                            if (selection.isValid) {
                              widget.controller.formatSelection(
                                Attribute.clone(Attribute.bold, null),
                              );
                              widget.controller.formatSelection(
                                Attribute.clone(Attribute.italic, null),
                              );
                              widget.controller.formatSelection(
                                Attribute.clone(Attribute.underline, null),
                              );
                              widget.controller.formatSelection(
                                Attribute.clone(Attribute.strikeThrough, null),
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    iconSize: 20.0,
                    tooltip: 'Close',
                  ),
                ],
              ),
            ),
            // Editor
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextSelectionTheme(
                  data: TextSelectionThemeData(
                    selectionColor: _primaryBlue.withOpacity(0.3),
                    selectionHandleColor: _primaryBlue,
                  ),
                  child: QuillEditor.basic(
                    controller: widget.controller,
                    focusNode: _focusNode,
                  ),
                ),
              ),
            ),
            // Status bar
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(4.0),
                  bottomRight: Radius.circular(4.0),
                ),
                color:
                    widget.isDarkTheme
                        ? Colors.grey.shade800
                        : Color(0xFFEBEBEB),
                border: Border(
                  top: BorderSide(
                    color:
                        widget.isDarkTheme
                            ? Colors.grey.shade600
                            : Color(0xFFCCCCCC),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    '$_wordCount Words',
                    // style: TextStyle(
                    //   color:
                    //       widget.isDarkTheme
                    //           ? Colors.grey.shade400
                    //           : Colors.grey.shade600,
                    //   fontSize: 12.0,
                    // ),
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color:
                          widget.isDarkTheme
                              ? Colors.grey.shade400
                              : Colors.grey.shade600,
                      fontSize: 12.0,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Color item widget for color picker
class _ColorItem extends StatelessWidget {
  final Color color;
  final String name;
  final TextStyle? textStyle; // Add this line
  const _ColorItem({required this.color, required this.name, this.textStyle});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.grey),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          name,
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontWeight: FontManager.medium,
            fontSize: _getResponsiveValueFontSize(context),
            color: Color(0xFF333333),
          ),
        ),
      ],
    );
  }
}

double _getResponsiveIconSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 32.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 32.0; // Large
  } else if (screenWidth >= 1280) {
    return 28.0; // Medium
  } else if (screenWidth >= 768) {
    return 24.0; // Small
  } else {
    return 24.0; // Extra Small (fallback for very small screens)
  }
}

double _getResponsiveIconSizeSmall(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 24.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 24.0; // Large
  } else if (screenWidth >= 1280) {
    return 22.0; // Medium
  } else if (screenWidth >= 768) {
    return 20.0; // Small
  } else {
    return 20.0; // Extra Small (fallback for very small screens)
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px)
  } else {
    return 40.0; // Default for very small screens
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(
      horizontal: 12.0,
      vertical: 3.0,
    ); // Large// Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 2.0,
    ); // Medium// Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 1.0,
    ); // Default for very small screens
  }
}
